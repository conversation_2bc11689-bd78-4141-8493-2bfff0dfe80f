1. first when you receive searchQuery check if it is not lat and long 
       - if lat and long 
            - check if they are valid lat and long 
            - check that they are falling into rwanda else return 
2. if search is a text 
       - check if filterData is not all , else check the filterData then redirect search 
3. if search is all 
     - step one detect the pattern in search 
     [text1,text2] this means users is searching for district 
     [text1,text2,text3] this means users is searching for sector 
     [text1,text2,text3,text4] this means users is searching for cell 
     [text1,text2,text3,text4,text5] this means users is searching for village 
     [text1,text2,text3,text4,text5,text6] this means users is searching for place 

     - step two , is to check in every text if there is no patter
       like [ umurenge wa text3, umugudugu wa text5]
       here we will search Village with name text5 and whihc has sector name text3
    - if user typed [ akagari ka text4 , umurenge wa text3]
    we will search cell with name text4 and which has sector name text3
    - if user typed [ umudugudu wa text5, umurenge wa text3]
    we will search village with name text5 and which has sector name text3
    - if user typed [ umudugudu wa text5, umurenge wa text3, akagari ka text4]
    we will search village with name text5 and which has sector name text3 and which has cell name text4
 4. to determine search pattern we will have common key word for every patter like 
 5. when the search has no pattern we will search for the text in all fields 
       
also add option to cache, all districts , cell , sectors , province , and villages , create a stand alone method for every model here to make it easy to cache , make other private model to hold all search pattern terms , also add and option to not return GeoJson when a request if from an API request , 

heaalthfacility are removed in this new search index , to validate if the long and lat are not in rwanda skip this stage but add the empy method for it , also include the language filter based on filter =