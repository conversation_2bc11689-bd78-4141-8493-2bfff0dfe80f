<?php

use App\Http\Controllers\MapController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
     return response()->json('Welcome to the Onrwanda Geo API!');
});

Route::prefix('search')->group(function () {
    Route::get('', [MapController::class, 'ApiSearch'])->name('api.search');
    Route::get('location', [MapController::class, 'searchLocation'])->name('api.search.location');
});