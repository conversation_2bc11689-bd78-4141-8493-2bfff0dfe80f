<?php

namespace App\Http\Controllers;

use App\Data\SearchData;
use App\Http\Requests\SearchRequest;
use App\Services\GorillaIndexingService;
use Illuminate\Http\Request;

class GorillaIndexingController extends Controller
{
    public function __construct(private GorillaIndexingService $service) {}
    public function search(SearchRequest $request)
    {

        $data = SearchData::from($request->validated());
        $searchResults = $this->service->search($data);
        return response()->json($searchResults);
    }
}
