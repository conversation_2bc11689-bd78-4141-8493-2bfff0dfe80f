1. user can create acount 
2. user can login 
3. user can reset password
4. user can update their profile 
5. user can delete theri browser session 

6. user can search using text 
7. user can search using latitude and longtude 

8. during search Gorilla Index engine will be used to search for the following:
      -able to identify the following:
        -province
        -district
        -sector
        -cell
        -village
        -health facility
        -pattern
            - here on pettern it has to know 
               [text, text] this is distict 
               [text,text,text] this is sector 
               [text,text,text,text] this is cell 
               [text,text,text,text,text] this is village 
               [text,text,text,text,text,text] this is place 
               - this has to be done locacly without any thord party service 


custom maps 

9. user can create custom map 
10. user can add custom fields to the map 
11. user can edit map 
12. user can share maps 
13. user can add custom place on map ( crud)
15. user can add custom polygon on map ( crud)
16. user can enable geofencing on custom place whne is a moving item
            - geofacing is a paid feature 
            - 
17. user can set custom multitle geofancing to moving item 
                 - when item is away 
                 - when item is close 
                 - when item is in range
18. types of supported geofacing ( all admistration and custom polygone )   
19. every moving item has API Key 
20. every map has ottion to have map dta , and one map can have more than one map data 
21. map data has map data item which is connected to place map item
22. map data has custom field and those custom fiels are the onse used to fuill mad data item on place map 
22. map data item   data has to be paginated                
23. with we will use Gorila data engine which will be between Gorila index and datamap 
         - data map items will have filters 
         - use can search on single map like main query 'drug , and filter by pharmacy , them pharmacy data may have priceing the get prcing low to high , thne user can select range , location to match , these dat can be passed into Gorilla travel engine which works with OSRm 
         - data map has to supporthtese data analyics in Gorilla data engine 
                       -     Generate heatmaps.
                       -     Perform choropleth mapping (coloring districts/sectors based on user data).
                       -     Find the nearest N points to a given location.
                       -     Perform "within" queries (e.g., "show me all my customers within the Gasabo district").
24. Gorilla Travel engine , will do 
           - geofacnig ncalculation 
           - navigation with from to and throught ( here it can connect to search engine to add more nearby locations )